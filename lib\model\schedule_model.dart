// ignore_for_file: prefer_const_constructors

import 'package:json_annotation/json_annotation.dart';

part 'schedule_model.g.dart';

@JsonSerializable()
class ScheduleModel {
  final String id;
  final String name;
  final String description;
  final String deviceId;
  final String controlId;
  final ScheduleType type;
  final DateTime startTime;
  final DateTime? endTime;
  final List<int> daysOfWeek; // 0=Sunday, 1=Monday, etc.
  final bool isActive;
  final bool isRepeating;
  final Map<String, dynamic> parameters;
  final DateTime createdAt;
  final DateTime updatedAt;

  ScheduleModel({
    required this.id,
    required this.name,
    required this.description,
    required this.deviceId,
    required this.controlId,
    required this.type,
    required this.startTime,
    this.endTime,
    required this.daysOfWeek,
    required this.isActive,
    required this.isRepeating,
    required this.parameters,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ScheduleModel.fromJson(Map<String, dynamic> json) =>
      _$ScheduleModelFromJson(json);

  Map<String, dynamic> toJson() => _$ScheduleModelToJson(this);

  ScheduleModel copyWith({
    String? id,
    String? name,
    String? description,
    String? deviceId,
    String? controlId,
    ScheduleType? type,
    DateTime? startTime,
    DateTime? endTime,
    List<int>? daysOfWeek,
    bool? isActive,
    bool? isRepeating,
    Map<String, dynamic>? parameters,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ScheduleModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      deviceId: deviceId ?? this.deviceId,
      controlId: controlId ?? this.controlId,
      type: type ?? this.type,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      daysOfWeek: daysOfWeek ?? this.daysOfWeek,
      isActive: isActive ?? this.isActive,
      isRepeating: isRepeating ?? this.isRepeating,
      parameters: parameters ?? this.parameters,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get formattedStartTime {
    return '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
  }

  String get formattedEndTime {
    if (endTime == null) return '';
    return '${endTime!.hour.toString().padLeft(2, '0')}:${endTime!.minute.toString().padLeft(2, '0')}';
  }

  String get daysOfWeekString {
    if (daysOfWeek.isEmpty) return 'Never';
    if (daysOfWeek.length == 7) return 'Every day';
    
    List<String> dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return daysOfWeek.map((day) => dayNames[day]).join(', ');
  }

  bool get isScheduledForToday {
    int today = DateTime.now().weekday % 7; // Convert to 0=Sunday format
    return daysOfWeek.contains(today);
  }
}

enum ScheduleType {
  @JsonValue('time_based')
  timeBased,
  @JsonValue('sensor_based')
  sensorBased,
  @JsonValue('manual')
  manual,
}

@JsonSerializable()
class AutomationRule {
  final String id;
  final String name;
  final String description;
  final String deviceId;
  final String controlId;
  final List<AutomationCondition> conditions;
  final List<AutomationAction> actions;
  final bool isActive;
  final AutomationLogic logic; // AND or OR for multiple conditions
  final DateTime createdAt;
  final DateTime updatedAt;

  AutomationRule({
    required this.id,
    required this.name,
    required this.description,
    required this.deviceId,
    required this.controlId,
    required this.conditions,
    required this.actions,
    required this.isActive,
    required this.logic,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AutomationRule.fromJson(Map<String, dynamic> json) =>
      _$AutomationRuleFromJson(json);

  Map<String, dynamic> toJson() => _$AutomationRuleToJson(this);

  AutomationRule copyWith({
    String? id,
    String? name,
    String? description,
    String? deviceId,
    String? controlId,
    List<AutomationCondition>? conditions,
    List<AutomationAction>? actions,
    bool? isActive,
    AutomationLogic? logic,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AutomationRule(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      deviceId: deviceId ?? this.deviceId,
      controlId: controlId ?? this.controlId,
      conditions: conditions ?? this.conditions,
      actions: actions ?? this.actions,
      isActive: isActive ?? this.isActive,
      logic: logic ?? this.logic,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class AutomationCondition {
  final String sensorType;
  final ComparisonOperator operator;
  final double value;
  final String unit;

  AutomationCondition({
    required this.sensorType,
    required this.operator,
    required this.value,
    required this.unit,
  });

  factory AutomationCondition.fromJson(Map<String, dynamic> json) =>
      _$AutomationConditionFromJson(json);

  Map<String, dynamic> toJson() => _$AutomationConditionToJson(this);

  String get description {
    String operatorText = '';
    switch (operator) {
      case ComparisonOperator.greaterThan:
        operatorText = '>';
        break;
      case ComparisonOperator.lessThan:
        operatorText = '<';
        break;
      case ComparisonOperator.equalTo:
        operatorText = '=';
        break;
      case ComparisonOperator.greaterThanOrEqual:
        operatorText = '>=';
        break;
      case ComparisonOperator.lessThanOrEqual:
        operatorText = '<=';
        break;
    }
    return '$sensorType $operatorText $value $unit';
  }

  bool evaluate(double currentValue) {
    switch (operator) {
      case ComparisonOperator.greaterThan:
        return currentValue > value;
      case ComparisonOperator.lessThan:
        return currentValue < value;
      case ComparisonOperator.equalTo:
        return currentValue == value;
      case ComparisonOperator.greaterThanOrEqual:
        return currentValue >= value;
      case ComparisonOperator.lessThanOrEqual:
        return currentValue <= value;
    }
  }
}

@JsonSerializable()
class AutomationAction {
  final String controlId;
  final ActionType actionType;
  final Map<String, dynamic> parameters;

  AutomationAction({
    required this.controlId,
    required this.actionType,
    required this.parameters,
  });

  factory AutomationAction.fromJson(Map<String, dynamic> json) =>
      _$AutomationActionFromJson(json);

  Map<String, dynamic> toJson() => _$AutomationActionToJson(this);

  String get description {
    switch (actionType) {
      case ActionType.turnOn:
        return 'Turn ON';
      case ActionType.turnOff:
        return 'Turn OFF';
      case ActionType.setValue:
        return 'Set to ${parameters['value']}';
      case ActionType.toggle:
        return 'Toggle';
    }
  }
}

enum AutomationLogic {
  @JsonValue('and')
  and,
  @JsonValue('or')
  or,
}

enum ComparisonOperator {
  @JsonValue('gt')
  greaterThan,
  @JsonValue('lt')
  lessThan,
  @JsonValue('eq')
  equalTo,
  @JsonValue('gte')
  greaterThanOrEqual,
  @JsonValue('lte')
  lessThanOrEqual,
}

enum ActionType {
  @JsonValue('turn_on')
  turnOn,
  @JsonValue('turn_off')
  turnOff,
  @JsonValue('set_value')
  setValue,
  @JsonValue('toggle')
  toggle,
}

@JsonSerializable()
class ScheduleExecution {
  final String id;
  final String scheduleId;
  final DateTime executedAt;
  final bool success;
  final String? errorMessage;
  final Map<String, dynamic> result;

  ScheduleExecution({
    required this.id,
    required this.scheduleId,
    required this.executedAt,
    required this.success,
    this.errorMessage,
    required this.result,
  });

  factory ScheduleExecution.fromJson(Map<String, dynamic> json) =>
      _$ScheduleExecutionFromJson(json);

  Map<String, dynamic> toJson() => _$ScheduleExecutionToJson(this);
}
