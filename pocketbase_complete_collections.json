[{"id": "users", "name": "users", "type": "auth", "system": false, "schema": [{"id": "name", "name": "name", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "avatar", "name": "avatar", "type": "file", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "maxSize": 5242880, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "thumbs": null, "protected": false}}], "indexes": [], "listRule": "id = @request.auth.id", "viewRule": "id = @request.auth.id", "createRule": "", "updateRule": "id = @request.auth.id", "deleteRule": "id = @request.auth.id", "options": {"allowEmailAuth": true, "allowOAuth2Auth": true, "allowUsernameAuth": true, "exceptEmailDomains": null, "manageRule": null, "minPasswordLength": 8, "onlyEmailDomains": null, "requireEmail": false}}, {"id": "devices", "name": "devices", "type": "base", "system": false, "schema": [{"id": "deviceName", "name": "deviceName", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "deviceId", "name": "deviceId", "type": "text", "system": false, "required": true, "unique": true, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "location", "name": "location", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 500, "pattern": ""}}, {"id": "isActive", "name": "isActive", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "userId", "name": "userId", "type": "relation", "system": false, "required": true, "unique": false, "options": {"collectionId": "users", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": []}}], "indexes": ["CREATE INDEX idx_devices_user ON devices (userId)", "CREATE INDEX idx_devices_active ON devices (isActive)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "sensor_data", "name": "sensor_data", "type": "base", "system": false, "schema": [{"id": "deviceId", "name": "deviceId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "temperature", "name": "temperature", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}, {"id": "humidity", "name": "humidity", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}, {"id": "soilMoisture", "name": "soilMoisture", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}, {"id": "lightIntensity", "name": "lightIntensity", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}, {"id": "ph", "name": "ph", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}], "indexes": ["CREATE INDEX idx_sensor_device ON sensor_data (deviceId)", "CREATE INDEX idx_sensor_created ON sensor_data (created)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "controls", "name": "controls", "type": "base", "system": false, "schema": [{"id": "deviceId", "name": "deviceId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "namaKontrol", "name": "namaKontrol", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "idKontrol", "name": "idKontrol", "type": "number", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null}}, {"id": "isON", "name": "isON", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "automated", "name": "automated", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "parameter", "name": "parameter", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 500, "pattern": ""}}], "indexes": ["CREATE INDEX idx_controls_device ON controls (deviceId)", "CREATE INDEX idx_controls_id ON controls (idKontrol)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "schedules", "name": "schedules", "type": "base", "system": false, "schema": [{"id": "name", "name": "name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "description", "name": "description", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 1000, "pattern": ""}}, {"id": "deviceId", "name": "deviceId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "controlId", "name": "controlId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "type", "name": "type", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["time_based", "sensor_based", "manual"]}}, {"id": "startTime", "name": "startTime", "type": "date", "system": false, "required": true, "unique": false, "options": {"min": "", "max": ""}}, {"id": "endTime", "name": "endTime", "type": "date", "system": false, "required": false, "unique": false, "options": {"min": "", "max": ""}}, {"id": "daysOfWeek", "name": "daysOfWeek", "type": "json", "system": false, "required": false, "unique": false, "options": {}}, {"id": "isActive", "name": "isActive", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "isRepeating", "name": "isRepeating", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "parameters", "name": "parameters", "type": "json", "system": false, "required": false, "unique": false, "options": {}}], "indexes": ["CREATE INDEX idx_schedules_device ON schedules (deviceId)", "CREATE INDEX idx_schedules_active ON schedules (isActive)", "CREATE INDEX idx_schedules_type ON schedules (type)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "automation_rules", "name": "automation_rules", "type": "base", "system": false, "schema": [{"id": "name", "name": "name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "description", "name": "description", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 1000, "pattern": ""}}, {"id": "deviceId", "name": "deviceId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "controlId", "name": "controlId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "conditions", "name": "conditions", "type": "json", "system": false, "required": true, "unique": false, "options": {}}, {"id": "actions", "name": "actions", "type": "json", "system": false, "required": true, "unique": false, "options": {}}, {"id": "isActive", "name": "isActive", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "logic", "name": "logic", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["AND", "OR"]}}], "indexes": ["CREATE INDEX idx_automation_device ON automation_rules (deviceId)", "CREATE INDEX idx_automation_active ON automation_rules (isActive)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "schedule_executions", "name": "schedule_executions", "type": "base", "system": false, "schema": [{"id": "scheduleId", "name": "scheduleId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "executedAt", "name": "executedAt", "type": "date", "system": false, "required": true, "unique": false, "options": {"min": "", "max": ""}}, {"id": "status", "name": "status", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["success", "failed", "skipped"]}}, {"id": "result", "name": "result", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 2000, "pattern": ""}}, {"id": "error", "name": "error", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 2000, "pattern": ""}}], "indexes": ["CREATE INDEX idx_executions_schedule ON schedule_executions (scheduleId)", "CREATE INDEX idx_executions_status ON schedule_executions (status)", "CREATE INDEX idx_executions_date ON schedule_executions (executedAt)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}]